# 06 - Rendimiento y Optimización

## Optimización de Componentes React

*   **Memoización**: Usar `React.memo`, `useCallback` y `useMemo` para evitar re-renders innecesarios de componentes y funciones costosas.
*   **Virtualización de Listas**: Implementar virtualización para listas largas (ej. `react-window`, `react-virtualized`) para renderizar solo los elementos visibles.

## Carga de Recursos

*   **<PERSON>zy <PERSON> (Carga Perezosa)**: Utilizar `React.lazy` y `Suspense` para cargar componentes de forma asíncrona.
*   **Code Splitting (División de Código)**: Dividir el bundle de JavaScript en chunks más pequeños para reducir el tiempo de carga inicial.
*   **Optimización de Imágenes**: Comprimir y optimizar imágenes, usar formatos modernos (WebP), y considerar CDNs.

## Optimización de Consultas a Base de Datos

*   **Índices**: Asegurar que las columnas utilizadas en `WHERE`, `JOIN` y `ORDER BY` tengan índices adecuados.
*   **Consultas Eficientes**: Evitar `SELECT *`, usar `JOIN`s eficientes, y limitar el número de resultados.
*   **Caching de Datos**: Implementar caching a nivel de aplicación o servidor para datos frecuentemente accedidos.

## Monitoreo del Rendimiento

*   **Herramientas de Desarrollo**: Usar las herramientas de rendimiento del navegador (ej. Lighthouse, React DevTools Profiler) para identificar cuellos de botella.
*   **Monitoreo en Producción**: Implementar herramientas de monitoreo de rendimiento de aplicaciones (APM) como Sentry, Datadog, o New Relic.

## Crítico:

*   **Code splitting y lazy loading**
*   **Memoization strategies (React.memo, useMemo)**
*   **Bundle analysis y tree shaking**
*   **Image optimization**
*   **Web Vitals monitoring**
*   **Caching strategies**

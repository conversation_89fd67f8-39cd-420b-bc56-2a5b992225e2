# 02 - Arquitectura y Estructura de Componentes

## Organización de Directorios

*   **Estructura Basada en Características (Feature-based)**: Organizar el código por características o dominios de negocio en lugar de por tipo de archivo (ej. `src/features/auth/`, `src/features/products/`).
*   **Componentes**:
    *   `src/components/ui/`: Componentes UI genéricos y reutilizables (ej. botones, inputs, modales).
    *   `src/components/atoms/`, `src/components/molecules/`, `src/components/organisms/`: Usar Atomic Design, seguir esta convención.
    *   `src/components/shared/`: Componentes reutilizables que no son específicos de una característica.
*   **Hooks**: `src/hooks/` para hooks personalizados. Considerar subdirectorios para hooks relacionados con datos (`src/hooks/data/`) o UI (`src/hooks/ui/`).
*   **Servicios**: `src/services/` para lógica de negocio, llamadas a API, y otras utilidades que no son componentes ni hooks.
*   **Repositorios**: `src/repositories/` para la lógica de acceso a datos, abstracción de la base de datos o APIs externas.
*   **Contextos/Proveedores**: `src/contexts/` o `src/providers/` para el manejo de estado global con React Context API.
*   **Páginas**: `src/pages/` para componentes de nivel superior que representan rutas de la aplicación.
*   **Tipos**: `src/types/` para definiciones de tipos globales o compartidos.
*   **Monorepo**: Adoptar un enfoque de monorepo con un único `package.json` en la raíz para gestionar dependencias y scripts de manera centralizada.

## Patrones de Componentes

*   **Componentes Puros/Funcionales**: Preferir componentes funcionales con hooks.
*   **Separación de Preocupaciones**: Separar la lógica de presentación de la lógica de negocio. Los componentes deben ser lo más "tontos" posible, recibiendo datos y callbacks a través de props.
*   **Reutilización**: Diseñar componentes pensando en la reutilización. Evitar la duplicación de código.
*   **Composición sobre Herencia**: Favorecer la composición de componentes para construir interfaces complejas.

## Manejo de Estado

*   **Estado Local de Componentes**: Usar `useState` y `useReducer` para el estado interno de los componentes.
*   **Estado Global**:
    *   Para estado global simple o temas (ej. autenticación, configuración de usuario), usar React Context API (`src/contexts/` o `src/providers/`).
    *   Para estado global complejo o que requiere manejo de efectos secundarios y optimizaciones, considerar librerías como Redux, Zustand, Recoil, o Jotai.
*   **Estado de Servidor (Server State)**: Usar librerías como React Query o SWR para manejar el fetching, caching, sincronización y actualización de datos del servidor.

## Modularidad de Hooks

*   **Evitar Hooks Monolíticos**: Refactorizar hooks grandes que manejan múltiples funcionalidades distintas en hooks más pequeños y enfocados. Cada hook debe abordar una única preocupación (ej. gestión de estado para una característica específica, obtención de datos para un recurso particular).
*   **Promover la Reutilización**: Los hooks más pequeños y bien definidos son más fáciles de reutilizar en diferentes componentes y partes de la aplicación.
*   **Mejorar la Testabilidad**: Desglosar la lógica compleja en hooks más pequeños hace que las pruebas unitarias sean más manejables y efectivas.
*   **Mejorar la Legibilidad y Mantenibilidad**: Las unidades de código más pequeñas son más fáciles de entender, depurar y modificar.

## Debe incluir:

*   **Arquitectura por capas**: (presentation, business, data)
*   **Atomic Design para componentes UI**
*   **Custom hooks para lógica reutilizable**
*   **Barrel exports**: (index.ts files)
*   **Dependency injection patterns**
*   **Single Responsibility Principle**

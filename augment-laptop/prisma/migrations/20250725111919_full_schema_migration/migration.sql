-- CreateTable
CREATE TABLE "brands" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "website" VARCHAR(512),
    "logo_url" VARCHAR(512),
    "manufacturer_id" INTEGER,
    "created_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "brands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cpu_architectures" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "technology_nm" INTEGER,
    "release_year" INTEGER,

    CONSTRAINT "cpu_architectures_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cpus" (
    "id" SERIAL NOT NULL,
    "manufacturer_id" INTEGER NOT NULL,
    "model" VARCHAR(255) NOT NULL,
    "generation" VARCHAR(100),
    "cores" INTEGER NOT NULL,
    "threads" INTEGER NOT NULL,
    "base_clock_ghz" DECIMAL(4,2) NOT NULL,
    "boost_clock_ghz" DECIMAL(4,2),
    "tdp_watts" INTEGER,
    "cache_mb" INTEGER,
    "architecture_id" INTEGER,
    "supports_avx512" BOOLEAN DEFAULT false,

    CONSTRAINT "cpus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "displays" (
    "id" SERIAL NOT NULL,
    "laptop_id" INTEGER NOT NULL,
    "size_inches" DECIMAL(4,2) NOT NULL,
    "resolution_id" INTEGER NOT NULL,
    "refresh_rate" INTEGER,
    "panel_type_id" INTEGER,
    "is_touchscreen" BOOLEAN DEFAULT false,
    "brightness_nits" INTEGER,
    "color_gamut" VARCHAR(50),
    "hdr_support" BOOLEAN DEFAULT false,
    "response_time_ms" INTEGER,

    CONSTRAINT "displays_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gpus" (
    "id" SERIAL NOT NULL,
    "manufacturer_id" INTEGER NOT NULL,
    "model" VARCHAR(255) NOT NULL,
    "vram_gb" INTEGER NOT NULL,
    "memory_type_id" INTEGER,
    "base_clock_mhz" INTEGER,
    "boost_clock_mhz" INTEGER,
    "tdp_watts" INTEGER,
    "ray_tracing_support" BOOLEAN DEFAULT false,
    "tensor_cores" INTEGER,
    "cuda_cores" INTEGER,
    "compute_units" INTEGER,
    "supports_dlss" BOOLEAN DEFAULT false,
    "supports_fsr" BOOLEAN DEFAULT false,

    CONSTRAINT "gpus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "laptop_cpus" (
    "laptop_id" INTEGER NOT NULL,
    "cpu_id" INTEGER NOT NULL,
    "performance_score" INTEGER,

    CONSTRAINT "laptop_cpus_pkey" PRIMARY KEY ("laptop_id","cpu_id")
);

-- CreateTable
CREATE TABLE "laptop_gpus" (
    "laptop_id" INTEGER NOT NULL,
    "gpu_id" INTEGER NOT NULL,
    "is_discrete" BOOLEAN DEFAULT true,
    "performance_score" INTEGER,

    CONSTRAINT "laptop_gpus_pkey" PRIMARY KEY ("laptop_id","gpu_id")
);

-- CreateTable
CREATE TABLE "laptop_listings" (
    "id" SERIAL NOT NULL,
    "laptop_id" INTEGER NOT NULL,
    "source_id" INTEGER NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "url" VARCHAR(512) NOT NULL,
    "in_stock" BOOLEAN DEFAULT true,
    "shipping_cost" DECIMAL(8,2),
    "rating" DECIMAL(3,2),
    "reviews_count" INTEGER,
    "listing_date" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "free_shipping" BOOLEAN DEFAULT false,
    "estimated_delivery_days" INTEGER,
    "has_warranty" BOOLEAN DEFAULT true,
    "warranty_months" INTEGER,
    "processed" BOOLEAN DEFAULT false,

    CONSTRAINT "laptop_listings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "laptop_llm_compatibility" (
    "laptop_id" INTEGER NOT NULL,
    "llm_id" INTEGER NOT NULL,
    "estimated_tokens_per_second" INTEGER,
    "max_context_length" INTEGER,
    "score" INTEGER,
    "qualitative_assessment" TEXT,
    "can_run_offline" BOOLEAN DEFAULT false,
    "recommended_batch_size" INTEGER,
    "estimated_memory_usage_gb" DECIMAL(5,2),

    CONSTRAINT "laptop_llm_compatibility_pkey" PRIMARY KEY ("laptop_id","llm_id")
);

-- CreateTable
CREATE TABLE "laptop_os_compatibility" (
    "laptop_id" INTEGER NOT NULL,
    "os_id" INTEGER NOT NULL,
    "is_officially_supported" BOOLEAN DEFAULT true,
    "has_driver_issues" BOOLEAN DEFAULT false,
    "notes" TEXT,

    CONSTRAINT "laptop_os_compatibility_pkey" PRIMARY KEY ("laptop_id","os_id")
);

-- CreateTable
CREATE TABLE "laptop_ram" (
    "laptop_id" INTEGER NOT NULL,
    "ram_configuration_id" INTEGER NOT NULL,
    "slots_used" INTEGER NOT NULL DEFAULT 1,
    "max_slots" INTEGER,
    "expandable" BOOLEAN DEFAULT false,
    "max_supported_gb" INTEGER,

    CONSTRAINT "laptop_ram_pkey" PRIMARY KEY ("laptop_id","ram_configuration_id")
);

-- CreateTable
CREATE TABLE "laptop_scores" (
    "id" SERIAL NOT NULL,
    "laptop_id" INTEGER NOT NULL,
    "overall_score" INTEGER NOT NULL,
    "llm_performance_score" INTEGER NOT NULL,
    "battery_life_score" INTEGER,
    "build_quality_score" INTEGER,
    "display_score" INTEGER,
    "keyboard_score" INTEGER,
    "performance_score" INTEGER,
    "value_score" INTEGER,
    "thermal_score" INTEGER,
    "noise_score" INTEGER,
    "portability_score" INTEGER,
    "last_evaluated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "laptop_scores_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "laptop_storage" (
    "laptop_id" INTEGER NOT NULL,
    "storage_id" INTEGER NOT NULL,
    "is_primary" BOOLEAN DEFAULT false,
    "slot_type" VARCHAR(50),

    CONSTRAINT "laptop_storage_pkey" PRIMARY KEY ("laptop_id","storage_id")
);

-- CreateTable
CREATE TABLE "laptop_tags" (
    "laptop_id" INTEGER NOT NULL,
    "tag_id" INTEGER NOT NULL,
    "relevance_score" INTEGER DEFAULT 100,

    CONSTRAINT "laptop_tags_pkey" PRIMARY KEY ("laptop_id","tag_id")
);

-- CreateTable
CREATE TABLE "laptops" (
    "id" SERIAL NOT NULL,
    "model_name" VARCHAR(255) NOT NULL,
    "brand_id" INTEGER NOT NULL,
    "release_date" DATE,
    "description" TEXT,
    "image_url" VARCHAR(512),
    "is_available" BOOLEAN DEFAULT true,
    "msrp" DECIMAL(10,2),
    "created_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "laptops_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "llm_models" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "parameters_billions" DECIMAL(10,2),
    "quantization_bits" INTEGER,
    "min_ram_gb" INTEGER,
    "min_vram_gb" INTEGER,
    "requires_gpu" BOOLEAN DEFAULT true,
    "description" TEXT,
    "model_card_url" VARCHAR(512),
    "created_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "llm_models_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "manufacturers" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "website" VARCHAR(512),
    "country" VARCHAR(100),
    "founded_year" INTEGER,

    CONSTRAINT "manufacturers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "memory_types" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "max_bandwidth_gbps" DECIMAL(8,2),

    CONSTRAINT "memory_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "operating_systems" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "version" VARCHAR(50),
    "is_linux" BOOLEAN DEFAULT false,
    "is_windows" BOOLEAN DEFAULT false,
    "is_macos" BOOLEAN DEFAULT false,
    "min_ram_gb" INTEGER,
    "min_storage_gb" INTEGER,

    CONSTRAINT "operating_systems_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "panel_types" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "advantages" TEXT,
    "disadvantages" TEXT,

    CONSTRAINT "panel_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "physical_specs" (
    "id" SERIAL NOT NULL,
    "laptop_id" INTEGER NOT NULL,
    "weight_kg" DECIMAL(5,2) NOT NULL,
    "height_mm" DECIMAL(6,2),
    "width_mm" DECIMAL(6,2),
    "depth_mm" DECIMAL(6,2),
    "material" VARCHAR(100),
    "color" VARCHAR(100),
    "has_fingerprint_reader" BOOLEAN DEFAULT false,
    "has_webcam" BOOLEAN DEFAULT true,
    "webcam_resolution" VARCHAR(50),
    "has_backlit_keyboard" BOOLEAN DEFAULT false,

    CONSTRAINT "physical_specs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "port_types" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "max_data_rate_gbps" DECIMAL(8,2),
    "supports_power_delivery" BOOLEAN DEFAULT false,
    "max_power_delivery_watts" INTEGER,

    CONSTRAINT "port_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "price_history" (
    "id" SERIAL NOT NULL,
    "listing_id" INTEGER NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "recorded_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "price_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ram_configurations" (
    "id" SERIAL NOT NULL,
    "memory_type_id" INTEGER NOT NULL,
    "size_gb" INTEGER NOT NULL,
    "speed_mhz" INTEGER NOT NULL,
    "manufacturer_id" INTEGER,
    "is_dual_channel" BOOLEAN DEFAULT false,
    "cas_latency" INTEGER,

    CONSTRAINT "ram_configurations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "resolution_types" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "width" INTEGER NOT NULL,
    "height" INTEGER NOT NULL,
    "aspect_ratio" VARCHAR(10),

    CONSTRAINT "resolution_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scraping_history" (
    "id" SERIAL NOT NULL,
    "source_id" INTEGER NOT NULL,
    "start_time" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_time" TIMESTAMP(6),
    "status" VARCHAR(50) NOT NULL DEFAULT 'running',
    "items_found" INTEGER DEFAULT 0,
    "error_message" TEXT,

    CONSTRAINT "scraping_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scraping_sources" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "base_url" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "last_scraped_at" TIMESTAMPTZ(6),
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "selectors" JSONB,

    CONSTRAINT "scraping_sources_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sources" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "url" VARCHAR(512),
    "api_endpoint" VARCHAR(512),
    "is_active" BOOLEAN DEFAULT true,
    "last_updated" TIMESTAMP(6),
    "update_frequency" VARCHAR(50),
    "selectors" JSONB DEFAULT '{}',
    "last_scrape_attempt" TIMESTAMP(6),
    "scrape_success_count" INTEGER DEFAULT 0,
    "scrape_failure_count" INTEGER DEFAULT 0,

    CONSTRAINT "sources_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "storage_devices" (
    "id" SERIAL NOT NULL,
    "interface_id" INTEGER NOT NULL,
    "capacity_gb" INTEGER NOT NULL,
    "manufacturer_id" INTEGER,
    "read_speed_mbps" INTEGER,
    "write_speed_mbps" INTEGER,
    "is_nvme" BOOLEAN DEFAULT false,
    "has_dram_cache" BOOLEAN DEFAULT false,

    CONSTRAINT "storage_devices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "storage_interfaces" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "max_throughput_gbps" DECIMAL(8,2),

    CONSTRAINT "storage_interfaces_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tag_categories" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,

    CONSTRAINT "tag_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tags" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "category_id" INTEGER,
    "description" TEXT,

    CONSTRAINT "tags_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "brands_name_key" ON "brands"("name");

-- CreateIndex
CREATE UNIQUE INDEX "cpu_architectures_name_key" ON "cpu_architectures"("name");

-- CreateIndex
CREATE UNIQUE INDEX "cpus_manufacturer_id_model_generation_key" ON "cpus"("manufacturer_id", "model", "generation");

-- CreateIndex
CREATE INDEX "idx_displays_laptop_id" ON "displays"("laptop_id");

-- CreateIndex
CREATE UNIQUE INDEX "gpus_manufacturer_id_model_vram_gb_key" ON "gpus"("manufacturer_id", "model", "vram_gb");

-- CreateIndex
CREATE INDEX "idx_laptop_cpus_cpu_id" ON "laptop_cpus"("cpu_id");

-- CreateIndex
CREATE INDEX "idx_laptop_gpus_gpu_id" ON "laptop_gpus"("gpu_id");

-- CreateIndex
CREATE INDEX "idx_laptop_listings_laptop_id" ON "laptop_listings"("laptop_id");

-- CreateIndex
CREATE INDEX "idx_laptop_listings_source_id" ON "laptop_listings"("source_id");

-- CreateIndex
CREATE UNIQUE INDEX "laptop_listings_laptop_id_source_id_url_key" ON "laptop_listings"("laptop_id", "source_id", "url");

-- CreateIndex
CREATE INDEX "idx_laptop_ram_ram_id" ON "laptop_ram"("ram_configuration_id");

-- CreateIndex
CREATE UNIQUE INDEX "laptop_scores_laptop_id_key" ON "laptop_scores"("laptop_id");

-- CreateIndex
CREATE INDEX "idx_laptop_storage_storage_id" ON "laptop_storage"("storage_id");

-- CreateIndex
CREATE INDEX "idx_laptops_brand_id" ON "laptops"("brand_id");

-- CreateIndex
CREATE INDEX "idx_laptops_model_brand" ON "laptops"("model_name", "brand_id");

-- CreateIndex
CREATE UNIQUE INDEX "laptops_brand_id_model_name_key" ON "laptops"("brand_id", "model_name");

-- CreateIndex
CREATE UNIQUE INDEX "llm_models_name_key" ON "llm_models"("name");

-- CreateIndex
CREATE UNIQUE INDEX "manufacturers_name_key" ON "manufacturers"("name");

-- CreateIndex
CREATE UNIQUE INDEX "memory_types_name_key" ON "memory_types"("name");

-- CreateIndex
CREATE UNIQUE INDEX "operating_systems_name_key" ON "operating_systems"("name");

-- CreateIndex
CREATE UNIQUE INDEX "panel_types_name_key" ON "panel_types"("name");

-- CreateIndex
CREATE UNIQUE INDEX "physical_specs_laptop_id_key" ON "physical_specs"("laptop_id");

-- CreateIndex
CREATE INDEX "idx_physical_specs_laptop_id" ON "physical_specs"("laptop_id");

-- CreateIndex
CREATE UNIQUE INDEX "port_types_name_key" ON "port_types"("name");

-- CreateIndex
CREATE INDEX "idx_price_history_listing_id" ON "price_history"("listing_id");

-- CreateIndex
CREATE UNIQUE INDEX "resolution_types_name_key" ON "resolution_types"("name");

-- CreateIndex
CREATE UNIQUE INDEX "sources_name_key" ON "sources"("name");

-- CreateIndex
CREATE UNIQUE INDEX "storage_interfaces_name_key" ON "storage_interfaces"("name");

-- CreateIndex
CREATE UNIQUE INDEX "tag_categories_name_key" ON "tag_categories"("name");

-- CreateIndex
CREATE UNIQUE INDEX "tags_name_key" ON "tags"("name");

-- AddForeignKey
ALTER TABLE "brands" ADD CONSTRAINT "brands_manufacturer_id_fkey" FOREIGN KEY ("manufacturer_id") REFERENCES "manufacturers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cpus" ADD CONSTRAINT "cpus_architecture_id_fkey" FOREIGN KEY ("architecture_id") REFERENCES "cpu_architectures"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cpus" ADD CONSTRAINT "cpus_manufacturer_id_fkey" FOREIGN KEY ("manufacturer_id") REFERENCES "manufacturers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "displays" ADD CONSTRAINT "displays_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "displays" ADD CONSTRAINT "displays_panel_type_id_fkey" FOREIGN KEY ("panel_type_id") REFERENCES "panel_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "displays" ADD CONSTRAINT "displays_resolution_id_fkey" FOREIGN KEY ("resolution_id") REFERENCES "resolution_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gpus" ADD CONSTRAINT "gpus_manufacturer_id_fkey" FOREIGN KEY ("manufacturer_id") REFERENCES "manufacturers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "gpus" ADD CONSTRAINT "gpus_memory_type_id_fkey" FOREIGN KEY ("memory_type_id") REFERENCES "memory_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_cpus" ADD CONSTRAINT "laptop_cpus_cpu_id_fkey" FOREIGN KEY ("cpu_id") REFERENCES "cpus"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_cpus" ADD CONSTRAINT "laptop_cpus_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_gpus" ADD CONSTRAINT "laptop_gpus_gpu_id_fkey" FOREIGN KEY ("gpu_id") REFERENCES "gpus"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_gpus" ADD CONSTRAINT "laptop_gpus_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_listings" ADD CONSTRAINT "laptop_listings_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_listings" ADD CONSTRAINT "laptop_listings_source_id_fkey" FOREIGN KEY ("source_id") REFERENCES "sources"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_llm_compatibility" ADD CONSTRAINT "laptop_llm_compatibility_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_llm_compatibility" ADD CONSTRAINT "laptop_llm_compatibility_llm_id_fkey" FOREIGN KEY ("llm_id") REFERENCES "llm_models"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_os_compatibility" ADD CONSTRAINT "laptop_os_compatibility_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_os_compatibility" ADD CONSTRAINT "laptop_os_compatibility_os_id_fkey" FOREIGN KEY ("os_id") REFERENCES "operating_systems"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_ram" ADD CONSTRAINT "laptop_ram_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_ram" ADD CONSTRAINT "laptop_ram_ram_configuration_id_fkey" FOREIGN KEY ("ram_configuration_id") REFERENCES "ram_configurations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_scores" ADD CONSTRAINT "laptop_scores_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_storage" ADD CONSTRAINT "laptop_storage_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_storage" ADD CONSTRAINT "laptop_storage_storage_id_fkey" FOREIGN KEY ("storage_id") REFERENCES "storage_devices"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_tags" ADD CONSTRAINT "laptop_tags_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptop_tags" ADD CONSTRAINT "laptop_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "laptops" ADD CONSTRAINT "laptops_brand_id_fkey" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "physical_specs" ADD CONSTRAINT "physical_specs_laptop_id_fkey" FOREIGN KEY ("laptop_id") REFERENCES "laptops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "price_history" ADD CONSTRAINT "price_history_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "laptop_listings"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ram_configurations" ADD CONSTRAINT "ram_configurations_manufacturer_id_fkey" FOREIGN KEY ("manufacturer_id") REFERENCES "manufacturers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ram_configurations" ADD CONSTRAINT "ram_configurations_memory_type_id_fkey" FOREIGN KEY ("memory_type_id") REFERENCES "memory_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "scraping_history" ADD CONSTRAINT "scraping_history_source_id_fkey" FOREIGN KEY ("source_id") REFERENCES "sources"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "storage_devices" ADD CONSTRAINT "storage_devices_interface_id_fkey" FOREIGN KEY ("interface_id") REFERENCES "storage_interfaces"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "storage_devices" ADD CONSTRAINT "storage_devices_manufacturer_id_fkey" FOREIGN KEY ("manufacturer_id") REFERENCES "manufacturers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "tags" ADD CONSTRAINT "tags_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "tag_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

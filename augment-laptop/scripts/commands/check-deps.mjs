import fs from 'fs';
import path from 'path';
import { findImports } from '../lib/dependency-analyzer.mjs';
import { rootDir } from '../utils/paths.mjs';
import logger from '../utils/logger.mjs';
import { depCheckConfig } from '../config/dependency-check.config.mjs';
import { execute } from '../utils/shell.mjs';

function getProjectDependencies() {
  const packageJsonPath = path.join(rootDir, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  return { ...packageJson.dependencies, ...packageJson.devDependencies };
}

function checkMissing(imports, dependencies) {
    logger.info('\n=== Checking for Missing Dependencies ===');
    const missing = [...imports].filter(imp => 
        !dependencies[imp] && 
        !depCheckConfig.nodeBuiltins.includes(imp)
    );

    if (missing.length > 0) {
        logger.error(`Found ${missing.length} missing dependencies:`);
        missing.forEach(dep => logger.log(`- ${dep}`));
        logger.info(`\nTo install, run: npm install ${missing.join(' ')}`);
        return false;
    }
    
    logger.success('No missing dependencies found.');
    return true;
}

function checkUnused(imports, dependencies) {
    logger.info('\n=== Checking for Unused Dependencies ===');
    const unused = Object.keys(dependencies).filter(dep =>
        !imports.has(dep) &&
        !depCheckConfig.ignoreUnused.some(ignored => dep.startsWith(ignored))
    );

    if (unused.length > 0) {
        logger.warn(`Found ${unused.length} potentially unused dependencies:`);
        unused.forEach(dep => logger.log(`- ${dep}`));
        logger.warn('\nNote: Some dependencies might be used by config files or scripts.');
        logger.warn(`To remove, run: npm uninstall ${unused.join(' ')}`);
    } else {
        logger.success('No unused dependencies found.');
    }
    return unused.length === 0;
}

function checkVulnerabilities() {
    logger.info('\n=== Checking for Vulnerabilities ===');
    try {
        const auditOutput = execute('npm audit --json');
        const { vulnerabilities } = JSON.parse(auditOutput);
        const count = Object.keys(vulnerabilities).length;

        if (count > 0) {
            logger.error(`Found ${count} vulnerabilities. Run \`npm audit\` for details.`);
            return false;
        }
        
        logger.success('No vulnerabilities found.');
        return true;
    } catch (error) {
        // npm audit exits with non-zero code if vulns are found.
        // We need to check the JSON output to be sure.
        try {
            const { vulnerabilities } = JSON.parse(error.stdout.toString());
            const count = Object.keys(vulnerabilities).length;
            if (count > 0) {
                logger.error(`Found ${count} vulnerabilities. Run \`npm audit\` for details.`);
                return false;
            }
        } catch {
            logger.error('Could not run or parse `npm audit` output.');
        }
        return true; // Assume ok if audit fails to run
    }
}


export function checkDependencies() {
  logger.info('=== Verifying Project Dependencies ===');
  
  const dependencies = getProjectDependencies();
  const { imports, files } = findImports();

  logger.info(`Scanned ${files.length} files and found ${imports.size} unique package imports.`);

  const missingOk = checkMissing(imports, dependencies);
  const unusedOk = checkUnused(imports, dependencies);
  const auditOk = checkVulnerabilities();

  if (missingOk && unusedOk && auditOk) {
    logger.success('\n✅ Dependency check passed successfully!');
  } else {
    logger.error('\n❌ Dependency check failed with issues.');
    process.exit(1);
  }
}

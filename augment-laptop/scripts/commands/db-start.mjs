import fs from 'fs';
import { supabaseDir } from '../utils/paths.mjs';
import logger from '../utils/logger.mjs';
import { executeInherit } from '../utils/shell.mjs';

export async function startSupabase() {
  logger.info('Checking Supabase environment...');

  if (!fs.existsSync(supabaseDir)) {
    logger.error(`Supabase project directory not found at: ${supabaseDir}`);
    logger.error('Please ensure the path is configured correctly in scripts/utils/paths.mjs');
    process.exit(1);
  }

  // A simple check to see if docker-compose.yml exists
  if (!fs.existsSync(`${supabaseDir}/docker-compose.yml`)) {
      logger.error(`docker-compose.yml not found in: ${supabaseDir}`);
      logger.error('The specified Supabase directory seems to be incorrect or incomplete.');
      process.exit(1);
  }

  try {
    logger.info('Attempting to start Supabase services via Docker Compose...');
    executeInherit('docker compose up -d', { cwd: supabaseDir });
    logger.success('Supabase services started (or were already running).');
    logger.info('It might take a few moments for all services to be fully available.');
  } catch {
    logger.error('Failed to start Supabase services.');
    process.exit(1);
  }
}

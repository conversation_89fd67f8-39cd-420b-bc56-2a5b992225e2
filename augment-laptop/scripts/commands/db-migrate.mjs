import { runMigrations } from '../lib/migrator.mjs';
import logger from '../utils/logger.mjs';

export async function migrateDatabase(options) {
  try {
    const specificFiles = options.file ? [options.file] : [];
    
    if (specificFiles.length > 0) {
        logger.info(`Running specific migration file: ${specificFiles.join(', ')}`);
    } else {
        logger.info('Running all migration files.');
    }

    await runMigrations(specificFiles);
  } catch {
    logger.error('Database migration command failed.');
    process.exit(1);
  }
}

import postgres from 'postgres';
import { dbConfig } from '../config/db.config.mjs';
import logger from '../utils/logger.mjs';

let sql;

export async function connect() {
  if (sql) {
    return sql;
  }

  logger.info('Connecting to the database...');
  try {
    sql = postgres(dbConfig.connectionString, {
      onnotice: (notice) => logger.warn(`DB Notice: ${notice.message}`),
      max: 1,
      idle_timeout: 3,
      connect_timeout: 10,
    });
    
    // Test connection
    const result = await sql`SELECT 1 AS "one"`;
    if (result[0].one === 1) {
      logger.success('Database connection successful.');
    } else {
        throw new Error('Database connection test failed.');
    }

    return sql;
  } catch (error) {
    logger.error('Failed to connect to the database.');
    logger.error(`Reason: ${error.message}`);
    throw error;
  }
}

export async function disconnect() {
  if (sql) {
    await sql.end();
    logger.info('Database connection closed.');
    sql = undefined;
  }
}

/**
 * Executes a SQL query.
 * @param {import('postgres').Sql} query The query to execute.
 * @returns {Promise<import('postgres').RowList<import('postgres').Row[]>>}
 */
export async function executeQuery(query) {
  const db = await connect();
  return db`${query}`;
}

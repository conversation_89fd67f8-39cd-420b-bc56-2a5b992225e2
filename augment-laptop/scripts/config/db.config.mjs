import dotenv from 'dotenv';
import { rootDir } from '../utils/paths.mjs';
import path from 'path';

dotenv.config({ path: path.join(rootDir, '.env') });

const {
  SUPABASE_URL,
  SUPABASE_SERVICE_KEY,
  DATABASE_URL,
} = process.env;

if (!DATABASE_URL && (!SUPABASE_URL || !SUPABASE_SERVICE_KEY)) {
  throw new Error(
    'Database credentials are not defined. Please provide DATABASE_URL or SUPABASE_URL and SUPABASE_SERVICE_KEY in your .env file.'
  );
}

// Prefer direct DATABASE_URL if available, otherwise construct from Supabase vars
const connectionString = DATABASE_URL || `postgresql://postgres:${SUPABASE_SERVICE_KEY}@${new URL(SUPABASE_URL).hostname}:5432/postgres`;

export const dbConfig = {
  connectionString,
};

# Esquema Detallado de Base de Datos - LLM Laptop Lens

Este documento proporciona una descripción detallada del esquema de base de datos utilizado por LLM Laptop Lens.

**Versión:** 1.2.0 (6 de Mayo 2025)

## Tablas Principales

### Catálogos Básicos

#### `manufacturers`
Almacena información sobre los fabricantes de laptops y componentes.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(100) | Nombre del fabricante | NOT NULL, UNIQUE |
| website | VARCHAR(512) | Sitio web oficial | |
| country | VARCHAR(100) | País de origen | |
| founded_year | INT | Año de fundación | |

#### `brands`
Almacena información sobre las marcas de laptops.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(255) | Nombre de la marca | NOT NULL, UNIQUE |
| website | VARCHAR(512) | Sitio web oficial | |
| logo_url | VARCHAR(512) | URL del logo | |
| manufacturer_id | INT | Referencia al fabricante | REFERENCES manufacturers(id) |
| created_at | TIMESTAMP | Fecha de creación | DEFAULT CURRENT_TIMESTAMP |

#### `sources`
Almacena información sobre las fuentes de datos (tiendas, sitios web).

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(255) | Nombre de la fuente | NOT NULL, UNIQUE |
| url | VARCHAR(512) | URL de la fuente | |
| api_endpoint | VARCHAR(512) | Endpoint de API | |
| is_active | BOOLEAN | Estado de actividad | DEFAULT TRUE |
| last_updated | TIMESTAMP | Última actualización | |
| update_frequency | VARCHAR(50) | Frecuencia de actualización | |
| selectors | JSONB | Selectores para scraping | DEFAULT '{}' |
| last_scrape_attempt | TIMESTAMP | Último intento de scraping | |
| scrape_success_count | INT | Conteo de scraping exitoso | DEFAULT 0 |
| scrape_failure_count | INT | Conteo de scraping fallido | DEFAULT 0 |

### Laptops y Componentes

#### `laptops`
Tabla principal que almacena información básica de laptops.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| model_name | VARCHAR(255) | Nombre del modelo | NOT NULL |
| brand_id | INT | Referencia a la marca | NOT NULL, REFERENCES brands(id) |
| release_date | DATE | Fecha de lanzamiento | |
| description | TEXT | Descripción | |
| image_url | VARCHAR(512) | URL de la imagen | |
| is_available | BOOLEAN | Disponibilidad | DEFAULT TRUE |
| msrp | NUMERIC(10, 2) | Precio sugerido | |
| created_at | TIMESTAMP | Fecha de creación | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | Fecha de actualización | DEFAULT CURRENT_TIMESTAMP |

**Índices:**
- `idx_laptops_model_brand` en (model_name, brand_id)

**Restricciones:**
- UNIQUE (brand_id, model_name)

#### `displays`
Almacena especificaciones de pantallas.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| size_inches | NUMERIC(4, 2) | Tamaño en pulgadas | NOT NULL |
| resolution_id | INT | Referencia al tipo de resolución | NOT NULL, REFERENCES resolution_types(id) |
| refresh_rate | INT | Tasa de refresco (Hz) | |
| panel_type_id | INT | Referencia al tipo de panel | REFERENCES panel_types(id) |
| is_touchscreen | BOOLEAN | Es táctil | DEFAULT FALSE |
| brightness_nits | INT | Brillo en nits | |
| color_gamut | VARCHAR(50) | Gama de colores | |
| hdr_support | BOOLEAN | Soporte HDR | DEFAULT FALSE |
| response_time_ms | INT | Tiempo de respuesta (ms) | |

#### `physical_specs`
Almacena especificaciones físicas de laptops.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id), UNIQUE |
| weight_kg | NUMERIC(5, 2) | Peso en kg | NOT NULL |
| height_mm | NUMERIC(6, 2) | Altura en mm | |
| width_mm | NUMERIC(6, 2) | Ancho en mm | |
| depth_mm | NUMERIC(6, 2) | Profundidad en mm | |
| material | VARCHAR(100) | Material principal | |
| color | VARCHAR(100) | Color | |
| has_fingerprint_reader | BOOLEAN | Tiene lector de huellas | DEFAULT FALSE |
| has_webcam | BOOLEAN | Tiene webcam | DEFAULT TRUE |
| webcam_resolution | VARCHAR(50) | Resolución de webcam | |
| has_backlit_keyboard | BOOLEAN | Tiene teclado retroiluminado | DEFAULT FALSE |

### Componentes Principales

#### `cpus`
Almacena información sobre procesadores.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| manufacturer_id | INT | Referencia al fabricante | NOT NULL, REFERENCES manufacturers(id) |
| model | VARCHAR(255) | Modelo | NOT NULL |
| generation | VARCHAR(100) | Generación | |
| cores | INT | Número de núcleos | NOT NULL |
| threads | INT | Número de hilos | NOT NULL |
| base_clock_ghz | NUMERIC(4, 2) | Frecuencia base (GHz) | NOT NULL |
| boost_clock_ghz | NUMERIC(4, 2) | Frecuencia turbo (GHz) | |
| tdp_watts | INT | TDP en watts | |
| cache_mb | INT | Caché en MB | |
| architecture_id | INT | Referencia a la arquitectura | REFERENCES cpu_architectures(id) |
| supports_avx512 | BOOLEAN | Soporte AVX512 | DEFAULT FALSE |

**Restricciones:**
- UNIQUE (manufacturer_id, model, generation)

#### `gpus`
Almacena información sobre tarjetas gráficas.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| manufacturer_id | INT | Referencia al fabricante | NOT NULL, REFERENCES manufacturers(id) |
| model | VARCHAR(255) | Modelo | NOT NULL |
| vram_gb | INT | Memoria VRAM (GB) | NOT NULL |
| memory_type_id | INT | Tipo de memoria | REFERENCES memory_types(id) |
| base_clock_mhz | INT | Frecuencia base (MHz) | |
| boost_clock_mhz | INT | Frecuencia turbo (MHz) | |
| tdp_watts | INT | TDP en watts | |
| ray_tracing_support | BOOLEAN | Soporte ray tracing | DEFAULT FALSE |
| tensor_cores | INT | Núcleos tensor | |
| cuda_cores | INT | Núcleos CUDA (NVIDIA) | |
| compute_units | INT | Unidades de cómputo (AMD) | |
| supports_dlss | BOOLEAN | Soporte DLSS | DEFAULT FALSE |
| supports_fsr | BOOLEAN | Soporte FSR | DEFAULT FALSE |

**Restricciones:**
- UNIQUE (manufacturer_id, model, vram_gb)

### Compatibilidad con LLMs

#### `llm_models`
Almacena información sobre modelos de lenguaje.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(255) | Nombre del modelo | NOT NULL, UNIQUE |
| parameters_billions | NUMERIC(10, 2) | Parámetros en billones | |
| quantization_bits | INT | Bits de cuantización | |
| min_ram_gb | INT | RAM mínima requerida (GB) | |
| min_vram_gb | INT | VRAM mínima requerida (GB) | |
| requires_gpu | BOOLEAN | Requiere GPU | DEFAULT TRUE |
| description | TEXT | Descripción | |
| model_card_url | VARCHAR(512) | URL de la tarjeta del modelo | |
| created_at | TIMESTAMP | Fecha de creación | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | Fecha de actualización | DEFAULT CURRENT_TIMESTAMP |

#### `model_config`
Almacena configuraciones específicas de modelos LLM.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| model_id | INT | Referencia al modelo LLM | REFERENCES llm_models(id) |
| name | VARCHAR(255) | Nombre de la configuración | NOT NULL |
| provider | VARCHAR(100) | Proveedor del modelo | |
| version | VARCHAR(50) | Versión | |
| context_length | INTEGER | Longitud de contexto | |
| max_tokens | INTEGER | Tokens máximos | |
| cost_per_1k_tokens | DECIMAL(10,4) | Costo por 1K tokens | |
| capabilities | TEXT[] | Capacidades | |
| last_updated | TIMESTAMP | Última actualización | DEFAULT CURRENT_TIMESTAMP |
| endpoint | TEXT | Endpoint de API | |
| created_at | TIMESTAMP | Fecha de creación | DEFAULT CURRENT_TIMESTAMP |

**Índices:**
- `idx_model_config_model_id` en (model_id)

#### `laptop_llm_compatibility`
Almacena información sobre compatibilidad entre laptops y modelos LLM.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| llm_id | INT | Referencia al modelo LLM | NOT NULL, REFERENCES llm_models(id) |
| estimated_tokens_per_second | INT | Tokens estimados por segundo | |
| max_context_length | INT | Longitud máxima de contexto | |
| score | INT | Puntuación de compatibilidad | |
| qualitative_assessment | TEXT | Evaluación cualitativa | |
| can_run_offline | BOOLEAN | Puede ejecutarse sin conexión | DEFAULT FALSE |
| recommended_batch_size | INT | Tamaño de lote recomendado | |
| estimated_memory_usage_gb | NUMERIC(5, 2) | Uso estimado de memoria (GB) | |

**Restricciones:**
- PRIMARY KEY (laptop_id, llm_id)

### Tablas de Relación

#### `laptop_cpus`
Relaciona laptops con CPUs.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| cpu_id | INT | Referencia al CPU | NOT NULL, REFERENCES cpus(id) |
| performance_score | INT | Puntuación de rendimiento | |

**Restricciones:**

- PRIMARY KEY (laptop_id, cpu_id)

#### `laptop_gpus`
Relaciona laptops con GPUs.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| gpu_id | INT | Referencia al GPU | NOT NULL, REFERENCES gpus(id) |
| is_discrete | BOOLEAN | Es GPU dedicada | DEFAULT TRUE |
| performance_score | INT | Puntuación de rendimiento | |

**Restricciones:**

- PRIMARY KEY (laptop_id, gpu_id)

#### `laptop_ram`
Relaciona laptops con configuraciones de RAM.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| ram_configuration_id | INT | Referencia a la configuración de RAM | NOT NULL, REFERENCES ram_configurations(id) |
| slots_used | INT | Slots utilizados | NOT NULL DEFAULT 1 |
| max_slots | INT | Slots máximos | |
| expandable | BOOLEAN | Es expandible | DEFAULT FALSE |
| max_supported_gb | INT | GB máximos soportados | |

**Restricciones:**
- PRIMARY KEY (laptop_id, ram_configuration_id)

#### `laptop_storage`
Relaciona laptops con dispositivos de almacenamiento.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| storage_id | INT | Referencia al almacenamiento | NOT NULL, REFERENCES storage_devices(id) |
| is_primary | BOOLEAN | Es almacenamiento primario | DEFAULT FALSE |
| slot_type | VARCHAR(50) | Tipo de slot | |

**Restricciones:**
- PRIMARY KEY (laptop_id, storage_id)

#### `laptop_listings`
Almacena listados de laptops en diferentes fuentes.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| source_id | INT | Referencia a la fuente | NOT NULL, REFERENCES sources(id) |
| price | NUMERIC(10, 2) | Precio | NOT NULL |
| url | VARCHAR(512) | URL del listado | NOT NULL |
| in_stock | BOOLEAN | Disponibilidad en stock | DEFAULT TRUE |
| shipping_cost | NUMERIC(8, 2) | Costo de envío | |
| rating | NUMERIC(3, 2) | Calificación | |
| reviews_count | INT | Número de reseñas | |
| listing_date | TIMESTAMP | Fecha del listado | DEFAULT CURRENT_TIMESTAMP |
| free_shipping | BOOLEAN | Envío gratuito | DEFAULT FALSE |
| estimated_delivery_days | INT | Días estimados de entrega | |
| has_warranty | BOOLEAN | Tiene garantía | DEFAULT TRUE |
| warranty_months | INT | Meses de garantía | |
| name | TEXT | Nombre del listado | |
| brand | TEXT | Marca en el listado | |
| source_url | TEXT | URL de la fuente | |
| specs | JSONB | Especificaciones en formato JSON | |

**Índices:**
- `idx_laptop_listings_specs` en specs (GIN)

**Restricciones:**
- UNIQUE (laptop_id, source_id, url)
- CHECK specs schema

#### `price_history`
Almacena historial de precios.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| listing_id | INT | Referencia al listado | NOT NULL, REFERENCES laptop_listings(id) |
| price | NUMERIC(10, 2) | Precio | NOT NULL |
| recorded_at | TIMESTAMP | Fecha de registro | DEFAULT CURRENT_TIMESTAMP |

### Tablas de Catálogo Adicionales

#### `panel_types`
Tipos de paneles de pantalla.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(50) | Nombre del tipo | NOT NULL, UNIQUE |
| description | TEXT | Descripción | |
| advantages | TEXT | Ventajas | |
| disadvantages | TEXT | Desventajas | |

#### `resolution_types`
Tipos de resolución de pantalla.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(50) | Nombre del tipo | NOT NULL, UNIQUE |
| width | INT | Ancho en píxeles | NOT NULL |
| height | INT | Alto en píxeles | NOT NULL |
| aspect_ratio | VARCHAR(10) | Relación de aspecto | |

#### `cpu_architectures`
Arquitecturas de CPU.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(50) | Nombre de la arquitectura | NOT NULL, UNIQUE |
| description | TEXT | Descripción | |
| technology_nm | INT | Proceso de fabricación (nm) | |
| release_year | INT | Año de lanzamiento | |

#### `memory_types`
Tipos de memoria.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(50) | Nombre del tipo | NOT NULL, UNIQUE |
| description | TEXT | Descripción | |
| max_bandwidth_gbps | NUMERIC(8, 2) | Ancho de banda máximo (GB/s) | |

#### `storage_interfaces`
Interfaces de almacenamiento.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(50) | Nombre de la interfaz | NOT NULL, UNIQUE |
| description | TEXT | Descripción | |
| max_throughput_gbps | NUMERIC(8, 2) | Rendimiento máximo (GB/s) | |

## Vistas

### `laptop_basic_specs`
Vista que proporciona especificaciones básicas de laptops.

```sql
CREATE VIEW laptop_basic_specs AS
SELECT
    l.id, l.model_name, b.name AS brand_name, b.manufacturer_id,
    m.name AS manufacturer_name,
    d.size_inches AS screen_size, rt.name AS resolution,
    pt.name AS panel_type, d.refresh_rate,
    cpu.model AS cpu_model, cpu.cores, cpu.threads,
    gpu.model AS gpu_model, gpu.vram_gb,
    rc.size_gb AS ram_gb, rc.speed_mhz AS ram_speed,
    mt.name AS memory_type,
    sd.capacity_gb AS storage_capacity,
    si.name AS storage_interface,
    ps.weight_kg,
    lscores.overall_score,
    lscores.llm_performance_score,
    ll.specs
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN manufacturers m ON b.manufacturer_id = m.id
LEFT JOIN displays d ON d.laptop_id = l.id
LEFT JOIN resolution_types rt ON d.resolution_id = rt.id
LEFT JOIN panel_types pt ON d.panel_type_id = pt.id
LEFT JOIN laptop_cpus lc ON lc.laptop_id = l.id
LEFT JOIN cpus cpu ON lc.cpu_id = cpu.id
LEFT JOIN laptop_gpus lg ON lg.laptop_id = l.id
LEFT JOIN gpus gpu ON lg.gpu_id = gpu.id
LEFT JOIN laptop_ram lr ON lr.laptop_id = l.id
LEFT JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id
LEFT JOIN memory_types mt ON rc.memory_type_id = mt.id
LEFT JOIN laptop_storage lst ON lst.laptop_id = l.id AND lst.is_primary = TRUE
LEFT JOIN storage_devices sd ON lst.storage_id = sd.id
LEFT JOIN storage_interfaces si ON sd.interface_id = si.id
LEFT JOIN physical_specs ps ON ps.laptop_id = l.id
LEFT JOIN laptop_scores lscores ON lscores.laptop_id = l.id
LEFT JOIN (
    SELECT DISTINCT ON (laptop_id) laptop_id, specs
    FROM laptop_listings
    WHERE specs IS NOT NULL
    ORDER BY laptop_id, id DESC
) ll ON ll.laptop_id = l.id;
```

### `laptop_best_prices`
Vista que proporciona los mejores precios por laptop.

```sql
CREATE VIEW laptop_best_prices AS
SELECT
    l.id AS laptop_id,
    l.model_name,
    b.name AS brand,
    MIN(ll.price) AS lowest_price,
    s.name AS source,
    ll.url
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN laptop_listings ll ON ll.laptop_id = l.id
JOIN sources s ON ll.source_id = s.id
WHERE ll.in_stock = TRUE
GROUP BY l.id, l.model_name, b.name, s.name, ll.url;
```

### `llm_compatible_laptops`
Vista que proporciona laptops compatibles con modelos LLM.

```sql
CREATE VIEW llm_compatible_laptops AS
SELECT
    l.id AS laptop_id,
    l.model_name,
    b.name AS brand,
    llm.name AS llm_model,
    llm.parameters_billions,
    llc.estimated_tokens_per_second,
    llc.score AS compatibility_score,
    llc.can_run_offline,
    llc.recommended_batch_size,
    llc.estimated_memory_usage_gb
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN laptop_llm_compatibility llc ON llc.laptop_id = l.id
JOIN llm_models llm ON llc.llm_id = llm.id
WHERE llc.score > 50
ORDER BY l.id, llc.score DESC;
```

## Tablas Adicionales

### Sistemas Operativos y Compatibilidad

#### `operating_systems`
Almacena información sobre sistemas operativos.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(100) | Nombre del sistema | NOT NULL, UNIQUE |
| version | VARCHAR(50) | Versión | |
| is_linux | BOOLEAN | Es Linux | DEFAULT FALSE |
| is_windows | BOOLEAN | Es Windows | DEFAULT FALSE |
| is_macos | BOOLEAN | Es macOS | DEFAULT FALSE |
| min_ram_gb | INT | RAM mínima requerida (GB) | |
| min_storage_gb | INT | Almacenamiento mínimo (GB) | |

#### `laptop_os_compatibility`
Relaciona laptops con sistemas operativos compatibles.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| os_id | INT | Referencia al sistema operativo | NOT NULL, REFERENCES operating_systems(id) |
| is_officially_supported | BOOLEAN | Soporte oficial | DEFAULT TRUE |
| has_driver_issues | BOOLEAN | Problemas de drivers | DEFAULT FALSE |
| notes | TEXT | Notas adicionales | |

**Restricciones:**
- PRIMARY KEY (laptop_id, os_id)

### Etiquetas y Categorización

#### `tag_categories`
Categorías de etiquetas para clasificación.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(100) | Nombre de la categoría | NOT NULL, UNIQUE |
| description | TEXT | Descripción | |

#### `tags`
Etiquetas para filtrado y categorización.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| name | VARCHAR(100) | Nombre de la etiqueta | NOT NULL, UNIQUE |
| category_id | INT | Referencia a la categoría | REFERENCES tag_categories(id) |
| description | TEXT | Descripción | |

#### `laptop_tags`
Relaciona laptops con etiquetas.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id) |
| tag_id | INT | Referencia a la etiqueta | NOT NULL, REFERENCES tags(id) |
| relevance_score | INT | Puntuación de relevancia | DEFAULT 100 |

**Restricciones:**
- PRIMARY KEY (laptop_id, tag_id)

### Puntuaciones y Evaluaciones

#### `laptop_scores`
Almacena puntuaciones de laptops en diferentes categorías.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| laptop_id | INT | Referencia a la laptop | NOT NULL, REFERENCES laptops(id), UNIQUE |
| overall_score | INT | Puntuación general | NOT NULL |
| llm_performance_score | INT | Puntuación de rendimiento LLM | NOT NULL |
| battery_life_score | INT | Puntuación de batería | |
| build_quality_score | INT | Puntuación de calidad de construcción | |
| display_score | INT | Puntuación de pantalla | |
| keyboard_score | INT | Puntuación de teclado | |
| performance_score | INT | Puntuación de rendimiento general | |
| value_score | INT | Puntuación de relación calidad-precio | |
| thermal_score | INT | Puntuación térmica | |
| noise_score | INT | Puntuación de ruido | |
| portability_score | INT | Puntuación de portabilidad | |
| last_evaluated_at | TIMESTAMP | Fecha de última evaluación | DEFAULT CURRENT_TIMESTAMP |

### Historial de Scraping

#### `scraping_history`
Almacena historial de operaciones de scraping.

| Columna | Tipo | Descripción | Restricciones |
|---------|------|-------------|---------------|
| id | SERIAL | Identificador único | PRIMARY KEY |
| source_id | INT | Referencia a la fuente | NOT NULL, REFERENCES sources(id) |
| start_time | TIMESTAMP | Hora de inicio | NOT NULL, DEFAULT CURRENT_TIMESTAMP |
| end_time | TIMESTAMP | Hora de finalización | |
| status | VARCHAR(50) | Estado del scraping | NOT NULL, DEFAULT 'running' |
| items_found | INT | Elementos encontrados | DEFAULT 0 |
| error_message | TEXT | Mensaje de error | |

## Cambios en el Esquema

### Versión 1.2.0 (Mayo 2023)

1. **Adición de la tabla `model_config`**
   - Almacena configuraciones específicas de modelos LLM
   - Incluye información sobre proveedores, versiones, y costos

2. **Actualización de la tabla `laptop_listings`**
   - Nuevos campos: `name`, `brand`, `source_url`, `specs`
   - Campo `specs` con estructura JSON para almacenar especificaciones adicionales

3. **Nuevos índices**
   - `idx_laptop_listings_specs` en `laptop_listings(specs)` usando GIN
   - `idx_model_config_model_id` en `model_config(model_id)`

4. **Actualización de vistas**
   - `laptop_basic_specs` ahora incluye información de `specs` de `laptop_listings`

### Versión 1.1.0 (Abril 2023)

1. **Adición de tablas de compatibilidad LLM**
   - `llm_models` para almacenar información de modelos LLM
   - `laptop_llm_compatibility` para relacionar laptops con modelos LLM

2. **Adición de sistema de etiquetas**
   - `tag_categories` para categorías de etiquetas
   - `tags` para etiquetas individuales
   - `laptop_tags` para relacionar laptops con etiquetas

3. **Adición de sistema de puntuación**
   - `laptop_scores` para almacenar puntuaciones en diferentes categorías

4. **Nuevas vistas**
   - `llm_compatible_laptops` para facilitar consultas de compatibilidad

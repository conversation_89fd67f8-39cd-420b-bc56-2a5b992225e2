-- <PERSON><PERSON><PERSON> para eliminar todas las tablas de la base de datos

DROP TABLE IF EXISTS price_history CASCADE;
DROP TABLE IF EXISTS laptop_listings CASCADE;
DROP TABLE IF EXISTS laptop_storage CASCADE;
DROP TABLE IF EXISTS laptop_ram CASCADE;
DROP TABLE IF EXISTS laptop_gpus CASCADE;
DROP TABLE IF EXISTS laptop_cpus CASCADE;
DROP TABLE IF EXISTS storage_devices CASCADE;
DROP TABLE IF EXISTS ram_configurations CASCADE;
DROP TABLE IF EXISTS gpus CASCADE;
DROP TABLE IF EXISTS cpus CASCADE;
DROP TABLE IF EXISTS physical_specs CASCADE;
DROP TABLE IF EXISTS displays CASCADE;
DROP TABLE IF EXISTS laptops CASCADE;
DROP TABLE IF EXISTS port_types CASCADE;
DROP TABLE IF EXISTS storage_interfaces CASCADE;
DROP TABLE IF EXISTS memory_types CASCADE;
DROP TABLE IF EXISTS cpu_architectures CASCADE;
DROP TABLE IF EXISTS resolution_types CASCADE;
DROP TABLE IF EXISTS panel_types CASCADE;
DROP TABLE IF EXISTS scraping_history CASCADE;
DROP TABLE IF EXISTS sources CASCADE;
DROP TABLE IF EXISTS brands CASCADE;
DROP TABLE IF EXISTS manufacturers CASCADE;
DROP TABLE IF EXISTS laptop_llm_compatibility CASCADE;
DROP TABLE IF EXISTS llm_models CASCADE;
DROP TABLE IF EXISTS laptop_os_compatibility CASCADE;
DROP TABLE IF EXISTS operating_systems CASCADE;
DROP TABLE IF EXISTS laptop_tags CASCADE;
DROP TABLE IF EXISTS tags CASCADE;
DROP TABLE IF EXISTS tag_categories CASCADE;
DROP TABLE IF EXISTS laptop_scores CASCADE;

DROP VIEW IF EXISTS llm_compatible_laptops CASCADE;
DROP VIEW IF EXISTS laptop_best_prices CASCADE;
DROP VIEW IF EXISTS laptop_basic_specs CASCADE;
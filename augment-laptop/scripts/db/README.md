# Scripts de Base de Datos y Supabase

Este directorio contiene scripts para gestionar la base de datos y los servicios de Supabase.

## `start-supabase.mjs`

Este script automatiza el proceso de inicio de los servicios de Supabase utilizando Docker Compose, asegurando que el entorno de desarrollo local sea consistente y fácil de levantar.

### Requisitos Previos

Antes de ejecutar el script, asegúrate de tener instalado:

- **<PERSON>er y Docker Compose:** [Guía de instalación de Docker](https://docs.docker.com/get-docker/)
- **Git:** [Guía de instalación de Git](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git)
- **Node.js y npm:** [Guía de instalación de Node.js](https://nodejs.org/en/download/)

### Configuración del Proyecto

Si es la primera vez que configuras el proyecto, sigue estos pasos para preparar tu entorno de Supabase:

1.  **Clona el repositorio de Supabase:**
    ```bash
    git clone --filter=blob:none --no-checkout https://github.com/supabase/supabase
    cd supabase
    git sparse-checkout set --cone docker && git checkout master
    cd ..
    ```

2.  **Crea el directorio para tu proyecto:**
    ```bash
    mkdir supabase-project
    ```

3.  **Copia los archivos de configuración de Docker:**
    ```bash
    cp -rf supabase/docker/* supabase-project
    ```

4.  **Copia las variables de entorno:**
    ```bash
    cp supabase/docker/.env.example supabase-project/.env
    ```
    Ajusta las variables en el archivo `.env` según sea necesario.

5.  **Descarga las imágenes de Docker:**
    ```bash
    cd supabase-project
    docker compose pull
    ```

### Uso del Script

Una vez completada la configuración, puedes usar el script `start-supabase.mjs` para iniciar los servicios.

```bash
node scripts/db/start-supabase.mjs
```

El script realizará las siguientes acciones:

1.  **Verifica la existencia del directorio `supabase-project`:** Si no lo encuentra, mostrará los pasos de configuración necesarios.
2.  **Comprueba si los servicios de Supabase ya están en ejecución:** Realiza una consulta al endpoint de salud (`http://localhost:8000/health`).
3.  **Inicia los servicios si no están activos:** Ejecuta `docker compose up -d` dentro del directorio `supabase-project`.

Este enfoque centraliza la lógica de inicio y facilita la integración en flujos de trabajo de desarrollo y despliegue.

# Arquitectura del Servicio de Scraping

## Diagrama de Componentes

```mermaid
graph TD
    A[BaseScraper] --> B{ProxyManager};
    A --> C{SelectorRegistry};
    A --> D(Pipeline);

    subgraph Pipeline
        direction LR
        E[Fetch] --> F[Parse] --> G[Validate] --> H[Normalize] --> I[Persist];
    end

    D --> E;
```

## Componentes

### Core

- **`BaseScraper`**: Clase abstracta que orquesta el proceso de scraping. Ejecuta un pipeline de middlewares.
- **`ProxyManager`**: Gestiona la rotación de proxies para evitar bloqueos.
- **`SelectorRegistry`**: Almacena y proporciona configuraciones de selectores para diferentes fuentes.

### Pipeline (Middlewares)

El pipeline es una secuencia de etapas (middlewares) que procesan los datos.

- **`FetchMiddleware`**: Realiza la petición HTTP a la fuente.
- **`ParsingMiddleware`**: Extrae los datos crudos del HTML/JSON usando los selectores apropiados.
- **`ValidationMiddleware`**: Valida que los datos extraídos cumplan con el esquema esperado.
- **`NormalizationMiddleware`**: Limpia y estandariza los datos (ej. convierte precios a un formato numérico).
- **`PersistenceMiddleware`**: Guarda los datos procesados en la base de datos.

### Tipos

- **`scraping.types.ts`**: Define todas las interfaces y tipos de datos utilizados en el servicio. All types should be strictly defined, avoiding allways `any`.
# Technology Stack Versions

This document outlines the core technologies and their versions used in the LLM Laptop Lens project. Maintaining an up-to-date record of these versions is crucial for development, debugging, and ensuring compatibility.

## Frontend

* **Framework:** Next.js (latest LTS inferred)
* **Language:** TypeScript (^5.8.3)
* **UI Components:** Shadcn UI (using Radix UI components, versions inferred from `package.json`)
* **Styling:** Tailwind CSS (^4.1.11)
* **State Management:** React Context API, React Query (^5.83.0)

## Backend

* **Runtime:** Node.js (using pnpm@10.13.1, version inferred from project setup)
* **Language:** TypeScript (^5.8.3)
* **ORM:** Prisma (^6.12.0)

## Database

* **Provider:** Local PostgreSQL
* **Database Engine:** PostgreSQL (latest LTS inferred)
* **Local Database:** SQLite (via Prisma, version inferred from `package.json`)

## Web Scraping

* **Core Library:** Firecrawl MCP (version inferred from `docs/` and `.clinerules/`)
* **Browser Automation:** Puppeteer (^24.15.0)

## Testing

* **Framework:** Vitest (^3.2.4)
* **Utilities:** React Testing Library (^16.3.0)
* **E2E Testing:** Playwright (^1.54.1)

## Development Tools

* **Linter:** ESLint (^9.31.0)
* **Formatter:** Prettier (version inferred from `.prettierrc.json`)
* **Build Tool:** Vite (^7.0.6)

## Version Control

* **System:** Git (version inferred from project setup)

---

**Note:** Specific versions for each dependency can be found in the `package.json` file. This document provides a high-level overview of the core technologies used.

# Reporte Técnico: Interacción con la API de Revolico.com

## Introducción

Este documento detalla la implementación técnica para la interacción con la API GraphQL de Revolico.com, específicamente para la obtención de listados de laptops. La implementación se ha diseñado para ser configurable, permitiendo ajustar parámetros como el rango de precios y el filtrado por fecha.

## Configuración de la API GraphQL

### Endpoint

La API GraphQL de Revolico.com está disponible en:

```
https://api.revolico.com/graphql
```

### Consulta GraphQL

La consulta principal utilizada para obtener listados de laptops es:

```graphql
query SearchAds(
  $categories: [Int!], 
  $provinces: [String!], 
  $phrase: String, 
  $priceGte: Float, 
  $priceLte: Float, 
  $updatedOnToOrder: Boolean, 
  $daysAgo: Int, 
  $page: Int!
) { 
  search(
    categories: $categories, 
    provinces: $provinces, 
    phrase: $phrase, 
    price_gte: $priceGte, 
    price_lte: $priceLte, 
    updated_on_to_order: $updatedOnToOrder, 
    days_ago: $daysAgo, 
    page: $page
  ) { 
    ads { 
      id 
      title 
      description 
      price 
      updated_on 
      province { 
        name 
      } 
      images { 
        medium 
      } 
      currency 
      phone_visible 
      emails { 
        email 
      } 
      phones { 
        number 
      } 
      attributes 
    } 
    pagination { 
      current_page 
      total_pages 
      total_items 
    } 
  } 
}
```

### Variables de la Consulta

La consulta acepta las siguientes variables:

| Variable | Tipo | Descripción |
|----------|------|-------------|
| `categories` | `[Int!]` | IDs de las categorías a buscar. Para laptops, usamos `[860]` |
| `provinces` | `[String!]` | Provincias para filtrar resultados (opcional) |
| `phrase` | `String` | Término de búsqueda, usamos "laptop" |
| `priceGte` | `Float` | Precio mínimo (configurable) |
| `priceLte` | `Float` | Precio máximo (configurable) |
| `updatedOnToOrder` | `Boolean` | Ordenar por fecha de actualización |
| `daysAgo` | `Int` | Filtrar anuncios actualizados en los últimos N días (configurable) |
| `page` | `Int!` | Número de página actual (requerido) |

## Implementación del Scraper

### Estructura de Configuración

La configuración del scraper se almacena en la base de datos en la tabla `scraping_sources`. El campo `selectors` contiene la configuración específica para la API GraphQL:

```json
{
  "graphql": {
    "query": "query SearchAds($categories: [Int!], $provinces: [String!], $phrase: String, $priceGte: Float, $priceLte: Float, $updatedOnToOrder: Boolean, $daysAgo: Int, $page: Int!) { ... }",
    "variables": {
      "categories": [860],
      "phrase": "laptop",
      "priceGte": 800,
      "priceLte": 2000,
      "updatedOnToOrder": true,
      "daysAgo": 7,
      "page": 1
    },
    "pageParam": "page",
    "maxPages": 5,
    "filters": {
      "priceRange": {
        "enabled": true,
        "min": 800,
        "max": 2000,
        "minParam": "priceGte",
        "maxParam": "priceLte"
      },
      "dateFilter": {
        "enabled": true,
        "daysAgo": 7,
        "param": "daysAgo"
      }
    },
    "dataMapping": {
      "items": "data.search.ads",
      "name": "title",
      "description": "description",
      "price": "price",
      "currency": "currency",
      "imageUrl": "images[0].medium",
      "publishDate": "updated_on",
      "specs": {
        "mapping": "attributes",
        "transforms": {
          "ram": "attributes.ram",
          "cpu": "attributes.processor",
          "gpu": "attributes.graphic_card",
          "storage": "attributes.storage"
        }
      },
      "contactInfo": {
        "phone": "phones[0].number",
        "email": "emails[0].email"
      }
    }
  }
}
```

### Características Principales

1. **Filtrado por Fecha de Publicación**:
   - Se utiliza el parámetro `daysAgo` para filtrar anuncios publicados o actualizados en los últimos días.
   - El campo `updatedOnToOrder` se establece en `true` para ordenar los resultados por fecha de actualización.
   - La configuración está en `selectors.graphql.filters.dateFilter`.

2. **Rango de Precios Dinámico**:
   - Los parámetros `priceGte` y `priceLte` permiten establecer un rango de precios configurable.
   - La configuración está en `selectors.graphql.filters.priceRange`.

3. **Paginación**:
   - Se utiliza el parámetro `page` para recorrer las páginas de resultados.
   - El campo `maxPages` limita la cantidad de páginas a procesar.

4. **Mapeo de Datos**:
   - La configuración `dataMapping` define cómo se transforman los datos de la API en objetos `LaptopData`.
   - Incluye mapeo para especificaciones técnicas y datos de contacto.

### Procesamiento de Datos

El scraper realiza las siguientes tareas de procesamiento:

1. **Extracción de Marca**: Identifica marcas comunes en el título del anuncio.
2. **Parseo de Especificaciones**: Extrae información estructurada sobre CPU, RAM, almacenamiento y GPU.
3. **Análisis de Descripción**: Si las especificaciones estructuradas no están disponibles, intenta extraerlas del texto descriptivo.
4. **Filtrado y Normalización**: Convierte los datos a formatos estándar y filtra información inválida.

## Ejemplos de Uso

### Ejemplo 1: Obtener Laptops en el Rango de Precio $800-$2000 Publicadas en la Última Semana

```json
{
  "variables": {
    "categories": [860],
    "phrase": "laptop",
    "priceGte": 800,
    "priceLte": 2000,
    "updatedOnToOrder": true,
    "daysAgo": 7,
    "page": 1
  }
}
```

### Ejemplo 2: Modificar la Configuración para un Rango de Precio Diferente

```sql
UPDATE public.scraping_sources
SET selectors = jsonb_set(
  selectors,
  '{graphql,variables,priceGte}',
  '1000'::jsonb
)
WHERE name = 'Revolico.com';

UPDATE public.scraping_sources
SET selectors = jsonb_set(
  selectors,
  '{graphql,variables,priceLte}',
  '3000'::jsonb
)
WHERE name = 'Revolico.com';
```

### Ejemplo 3: Cambiar el Filtro de Fecha para Ver Anuncios del Último Mes

```sql
UPDATE public.scraping_sources
SET selectors = jsonb_set(
  selectors,
  '{graphql,variables,daysAgo}',
  '30'::jsonb
)
WHERE name = 'Revolico.com';
```

## Estructura de Respuesta

La API devuelve un objeto JSON con la siguiente estructura:

```json
{
  "data": {
    "search": {
      "ads": [
        {
          "id": "12345",
          "title": "Laptop Dell Inspiron 15 i7 16GB RAM",
          "description": "Excelente laptop con procesador...",
          "price": 1200,
          "updated_on": "2023-05-10T12:00:00Z",
          "province": { "name": "La Habana" },
          "images": [{ "medium": "https://example.com/image1.jpg" }],
          "currency": "USD",
          "phone_visible": true,
          "emails": [{ "email": "<EMAIL>" }],
          "phones": [{ "number": "+53 12345678" }],
          "attributes": {
            "ram": "16GB DDR4",
            "processor": "Intel i7-10750H",
            "graphic_card": "NVIDIA GTX 1650",
            "storage": "512GB SSD"
          }
        }
      ],
      "pagination": {
        "current_page": 1,
        "total_pages": 10,
        "total_items": 100
      }
    }
  }
}
```

## Consideraciones y Limitaciones

1. **Límite de Peticiones**: La API de Revolico puede tener limitaciones en la cantidad de peticiones por minuto. El scraper implementa retrasos entre peticiones para evitar bloqueos.

2. **Disponibilidad de Datos**: No todos los anuncios tienen información estructurada completa. El scraper intenta extraer información adicional de la descripción cuando es necesario.

3. **Formato de Datos**: Los datos de especificaciones técnicas no tienen un formato estándar, lo que requiere algoritmos de análisis flexibles para extraer información útil. Se debe evitar el uso de `any` al procesar estos datos.

4. **Imágenes y Enlaces**: Algunos anuncios pueden tener imágenes o enlaces rotos. El scraper maneja estos casos devolviendo valores predeterminados o nulos según corresponda.

5. **Actualizaciones de la API**: La implementación debe ser revisada periódicamente para adaptarse a posibles cambios en la estructura de la API de Revolico.

## Conclusiones

La implementación del scraper para Revolico.com proporciona una solución configurable y robusta para obtener información sobre laptops en el mercado cubano. La capacidad de ajustar los parámetros de filtrado por precio y fecha facilita la obtención de datos relevantes y actualizados según las necesidades específicas del proyecto.
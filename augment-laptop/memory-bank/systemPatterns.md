# System Patterns

## Architecture

The application follows a modern web architecture using Next.js for the frontend and server-side logic.

- **Frontend:** Built with React and Next.js, using TypeScript for type safety.
- **Styling:** Tailwind CSS for styling, with components from `shadcn/ui`.
- **API:** Next.js API routes are used to handle backend logic, such as managing scraping jobs and sources.

## Key Technical Decisions

- **Component-Based UI:** The user interface is built using a component-based approach, promoting reusability and maintainability.
- **Server-Side Rendering (SSR):** Next.js is used for its SSR capabilities, which can improve performance and SEO.
- **Static Site Generation (SSG):** Where possible, SSG is used for pages that do not require dynamic data.

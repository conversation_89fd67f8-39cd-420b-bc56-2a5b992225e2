# Project Brief: Laptop LLM Finder

This project aims to build a web application that helps users find the best laptops for running large language models (LLMs) locally. The application will provide a curated list of laptops with detailed specifications and performance benchmarks.

## Core Requirements

- **Laptop Listings:** Display a list of laptops suitable for LLMs.
- **Detailed Specifications:** Show key specs like RAM, VRAM, processor, and storage.
- **Search and Filtering:** Allow users to search and filter laptops based on their needs.
- **Scraping Admin:** An admin interface to manage scraping jobs for gathering laptop data from various sources.

## Goals

- Create a user-friendly interface for finding LLM-compatible laptops.
- Provide accurate and up-to-date information.
- Streamline the process of managing data sources for laptop information.

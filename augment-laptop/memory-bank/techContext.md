# Tech Context

## Technologies

- **Framework:** Next.js
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **UI Components:** shadcn/ui
- **Package Manager:** pnpm

## Development Setup

- The project is set up as a standard Next.js application.
- To run the development server, use `pnpm dev`.
- Code is organized into `app`, `components`, `lib`, and `shared` directories.

## Dependencies

- Key dependencies are listed in `package.json`.
- The project uses `pnpm` for package management, so dependencies should be managed with `pnpm` commands (e.g., `pnpm add`, `pnpm install`).
